"""
LLM client for OpenAI and Gemini
"""

from app.core.logger import Structured<PERSON>ogger
import os
from typing import Optional
from llama_index.llms.openai import OpenAI
from llama_index.llms.gemini import Gemini

logger = StructuredLogger(__name__)


async def create_openai_client(api_keys_config) -> Optional[OpenAI]:
    """Create OpenAI LLM client from config"""
    try:
        # Get API keys from database config

        openai_key = None
        if api_keys_config and "OPENAI_API_KEY" in api_keys_config:
            openai_key = api_keys_config["OPENAI_API_KEY"]
        else:
            # Fallback to environment variable
            openai_key = os.getenv("OPENAI_API_KEY")

        if not openai_key:
            logger.issue("OpenAI API key not found")
            return None

        model_name = api_keys_config.get("OPENAI_MODEL_NAME", "gpt-4.1") if api_keys_config else "gpt-4o-mini"

        # Create llama-index OpenAI LLM instance
        llm = OpenAI(
            api_key=openai_key,
            model=model_name,
            temperature=0.00
        )
        logger.log(f"OpenAI LLM client created with model: {model_name}")
        return llm

    except Exception as e:
        logger.error(f"Failed to create OpenAI LLM client: {e}")
        return None


async def create_gemini_client(api_keys_config) -> Optional[Gemini]:
    """Create Gemini LLM client from config"""
    try:
        # Get API keys from database config

        gemini_key = None
        if api_keys_config and "GEMINI_API_KEY" in api_keys_config:
            gemini_key = api_keys_config["GEMINI_API_KEY"]
        else:
            # Fallback to environment variable
            gemini_key = os.getenv("GEMINI_API_KEY")

        if not gemini_key:
            logger.warning("Gemini API key not found")
            return None

        model_name = api_keys_config.get("GEMINI_MODEL_NAME", "gemini-2.5-pro") if api_keys_config else "gemini-1.5-pro"

        # Create llama-index Gemini LLM instance
        llm = Gemini(
            api_key=gemini_key,
            model=model_name,
            temperature=0.00
        )
        logger.info(f"Gemini LLM client created with model: {model_name}")
        return llm

    except Exception as e:
        logger.error(f"Failed to create Gemini LLM client: {e}")
        return None


class LLMClients:
    """Simple container for LLM clients"""

    def __init__(self, openai_client=None, gemini_client=None):
        self.openai = openai_client
        self.gemini = gemini_client


async def create_llm_clients(config_db) -> LLMClients:
    """Create LLM clients from config and environment"""
    try:
        api_keys_config = await config_db.config.find_one({"name": "api_keys"})
        openai_client = await create_openai_client(api_keys_config)
        gemini_client = await create_gemini_client(api_keys_config)

        return LLMClients(
            openai_client=openai_client,
            gemini_client=gemini_client
        )

    except Exception as e:
        logger.error(f"Failed to create LLM clients: {e}")
        return LLMClients()
