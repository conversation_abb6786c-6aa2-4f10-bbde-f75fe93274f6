from app.models.current_user import CurrentUser
from app.core.logger import StructuredLogger

from typing import Dict, Any

logger = StructuredLogger(__name__)


async def process_citation(citation_mapping:dict, current_user: CurrentUser):

    for key, value in citation_mapping.items():
        if value.get('tool') == 'najir_search':
            sentence_id = value.get('metadata', {}).get('sentence_id')
            if sentence_id:
                actual_sentence=await current_user.db.read_db.najir_split_documents.find_one({"metadata.sentence_id": sentence_id}, {"text": 1, "_id": 0})
                if actual_sentence:
                    print(f"Actual Sentence Found")
                    value['text'] = actual_sentence.get('text', '')
            

    return citation_mapping