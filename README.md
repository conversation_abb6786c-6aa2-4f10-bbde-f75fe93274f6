# 🏛️ Legal Backend API

> **Production-ready FastAPI backend** for legal document processing with AI-powered chat, multi-tenant architecture, and enterprise-grade security.

[![Python 3.12+](https://img.shields.io/badge/python-3.12+-blue.svg)](https://www.python.org/downloads/)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.115+-green.svg)](https://fastapi.tiangolo.com/)
[![MongoDB](https://img.shields.io/badge/MongoDB-Atlas-green.svg)](https://www.mongodb.com/)
[![Docker](https://img.shields.io/badge/Docker-Ready-blue.svg)](https://www.docker.com/)

## 📋 Table of Contents

- [🏛️ Legal Backend API](#️-legal-backend-api)
  - [📋 Table of Contents](#-table-of-contents)
  - [🚀 Quick Start](#-quick-start)
  - [📦 Package Structure](#-package-structure)
  - [🔧 Technology Stack](#-technology-stack)
  - [🔄 Query Flow Architecture](#-query-flow-architecture)
  - [🔐 Authentication & Authorization](#-authentication--authorization)
  - [🐳 Docker Setup](#-docker-setup)
  - [📚 API Documentation](#-api-documentation)
  - [🧪 Testing](#-testing)
  - [🚀 Deployment](#-deployment)

## 🚀 Quick Start

### Prerequisites

- **Python**: 3.12+
- **Package Manager**: UV (modern Python package manager)
- **Vector DB**: Qdrant (for document retrieval and search)
- **Database**: MongoDB Atlas (for post-processing metadata storage)

### Local Development

```bash
# Clone and setup
git clone <repository-url>
cd legal_backend

# Install dependencies with uv
uv sync --frozen

# Activate virtual environment
source .venv/bin/activate

# Run development server
uv run fastapi dev app/main.py --host 0.0.0.0 --port 8000
```

### Docker Production

```bash
# Build and run with Docker
HOST_PORT=8000 docker compose up -d --build

# Check logs
docker compose logs -f

# Stop services
docker compose down
```

## 📦 Package Structure

```
legal_backend/
├── 📁 app/                          # Main application
│   ├── 📁 core/                     # Core functionality
│   │   ├── auth_helpers.py         # Authentication utilities
│   │   ├── config.py               # Configuration management
│   │   ├── database.py             # MongoDB connection
│   │   ├── llm_client.py           # LLM integrations (OpenAI, Gemini)
│   │   ├── qdrant_client.py        # Vector database client
│   │   └── security.py             # JWT & encryption
│   ├── 📁 models/                   # Pydantic models
│   │   ├── user.py                 # User model
│   │   ├── permission.py           # Permission system
│   │   └── security.py             # Security models
│   └── 📁 v1/                      # API v1 endpoints
│       ├── 📁 api/chat/            # AI chat endpoints
│       │   ├── core/               # Chat core logic
│       │   └── routes/             # Chat API routes
│       ├── 📁 api/knowledgebase/   # Document management
│       └── 📁 api/users/           # User management
├── 📁 tests/                        # Test suite
├── 📁 dist/                         # Build artifacts
├── 📄 pyproject.toml               # Project dependencies
├── 📄 Dockerfile                   # Production container
├── 📄 docker-compose.yml           # Docker orchestration
└── 📄 README.md                    # This file
```

## 🔧 Technology Stack

### Core Framework
- **FastAPI**: Modern, fast web framework for APIs
- **Python 3.12**: Latest Python with performance improvements
- **uv**: Ultra-fast Python package manager

### Database & Storage
- **MongoDB**: Post-processing and metadata storage (Atlas support)
- **Qdrant**: Vector database for semantic search and document retrieval

### AI & ML
- **LlamaIndex**: Advanced RAG framework for document indexing and retrieval
- **OpenAI GPT**: Primary LLM provider for response generation
- **Google Gemini**: Alternative LLM provider
- **Reranker**: Cross-encoder model for result re-ranking

### Security & Auth
- **JWT**: Stateless authentication
- **RBAC/RBPAC**: Role-based access control
- **Argon2**: Password hashing
- **PyJWT**: JWT token handling

### Development & DevOps
- **Docker**: Containerization
- **Pytest**: Testing framework
- **Pre-commit**: Code quality
- **GitHub Actions**: CI/CD

## 🔄 Query Flow Architecture

### Simple Agent 3-Step Flow

```
┌─────────────────────────────────────────────────────────────────┐
│                        USER QUERY                               │
└─────────────────┬───────────────────────────────────────┘
                           │
                           ▼
┌─────────────────────────────────────────────────────────────────┐
│                    STEP 1: INTENT CLASSIFICATION                │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │   Query Parser  │  │ Intent Detector │  │ Context Builder │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
└─────────────────────────┬───────────────────────────────────────┘
                           │
                           ▼
┌─────────────────────────────────────────────────────────────────┐
│                    STEP 2: DOCUMENT RETRIEVAL                   │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │ Najir Search    │  │ Najir Summary   │  │ ACT Search      │  │
│  │ (Qdrant)        │  │ Search          │  │ (Qdrant)        │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
└─────────────────────────┬───────────────────────────────────────┘
                           │
                           ▼
┌─────────────────────────────────────────────────────────────────┐
│                    STEP 3: RESPONSE GENERATION                  │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │ Context Builder │  │ LLM Generator   │  │ Citation Engine │  │
│  │ (LlamaIndex)    │  │ (OpenAI/Gemini) │  │ (Custom)        │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
└─────────────────────────┬───────────────────────────────────────┘
                          │
                          ▼
┌─────────────────────────────────────────────────────────────────┐
│                     FINAL RESPONSE                              │
└─────────────────────────────────────────────────────────────────┘
```

### Detailed Flow Components

#### 1. Query Processing
```python
# app/v1/api/chat/core/simple_agent.py
class SimpleAgent:
    async def process_query(self, query: str, user_context: dict):
        # Step 1: Parse and classify query
        intent = await self.classify_intent(query)
        
        # Step 2: Route to appropriate search branch
        if intent.type == "najir_search":
            documents = await self.najir_search(query, user_context)
        elif intent.type == "najir_summary":
            documents = await self.najir_summary_search(query, user_context)
        elif intent.type == "act_search":
            documents = await self.act_search(query, user_context)
        
        # Step 3: Generate response with citations
        response = await self.generate_response(query, documents)
        
        return response
```

#### 2. Document Retrieval Pipeline
```python
# Three specialized search branches:
# 1. Najir Search - Primary legal document search
# 2. Najir Summary Search - Summarized legal content
# 3. ACT Search - Specific acts and regulations

# All branches use:
# - Qdrant for vector similarity search
# - LlamaIndex for document indexing
# - Reranker for result optimization
# - MongoDB for post-processing metadata storage
```

#### 3. LLM Integration
```python
# app/core/llm_client.py
class LLMClient:
    async def generate_response(
        self,
        query: str,
        context: List[Document],
        model: str = "gpt-4"
    ) -> str:
        # Use LlamaIndex for RAG
        return await self.llama_index.query(query, context)
```

## 🔐 Authentication & Authorization

### JWT Token Flow
```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Client    │    │   FastAPI   │    │  MongoDB    │
└──────┬──────┘    └──────┬──────┘    └──────┬──────┘
       │                  │                  │
       │ 1. Login         │                  │
       │─────────────────▶│                  │
       │                  │ 2. Validate      │
       │                  │─────────────────▶│
       │                  │                  │
       │                  │ 3. Generate JWT  │
       │                  │◀─────────────────│
       │ 4. JWT Token    │                  │
       │◀─────────────────│                  │
       │                  │                  │
       │ 5. API Request  │                  │
       │─────────────────▶│                  │
       │                  │ 6. Verify JWT    │
       │                  │─────────────────▶│
       │                  │                  │
       │ 7. Response     │                  │
       │◀─────────────────│                  │
```

### RBAC System
- **Roles**: admin, lawyer, paralegal, client
- **Permissions**: read, write, delete, share
- **Resources**: documents, cases, clients, users

## 🐳 Docker Setup

### Production Dockerfile
```dockerfile
FROM python:3.12-slim

# Install uv for fast dependency management
COPY --from=ghcr.io/astral-sh/uv:latest /uv /uvx /bin/

WORKDIR /app

# Copy and install dependencies
COPY pyproject.toml uv.lock ./
RUN uv sync --frozen --no-dev

# Copy application code
COPY . .

EXPOSE 8000
CMD ["uv", "run", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### Docker Compose Services
```yaml
services:
  legal-backend:
    build: .
    ports:
      - "${HOST_PORT:-8000}:8000"
    env_file: .env
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
```

## 📚 API Documentation

### Authentication Endpoints
```http
POST /api/v1/auth/login
POST /api/v1/auth/register
POST /api/v1/auth/refresh
DELETE /api/v1/auth/logout
```

### Document Management
```http
GET    /api/v1/documents          # List documents
POST   /api/v1/documents          # Upload document
GET    /api/v1/documents/{id}     # Get document
PUT    /api/v1/documents/{id}     # Update document
DELETE /api/v1/documents/{id}     # Delete document
```

### AI Chat Endpoints
```http
POST /api/v1/chat/query            # Send query
GET  /api/v1/chat/history          # Get chat history
POST /api/v1/chat/rag              # RAG-based query
```

### Knowledge Base
```http
POST /api/v1/kb/search             # Search documents
POST /api/v1/kb/upload             # Upload to KB
GET  /api/v1/kb/filters            # Get filter options
```

## 🧪 Testing

```bash
# Run all tests
uv run pytest

# Run with coverage
uv run pytest --cov=app --cov-report=html

# Run specific test file
uv run pytest tests/test_auth.py

# Run with verbose output
uv run pytest -v
```

## 🚀 Deployment

### Environment Variables
```bash
# Required
MONGODB_URL=mongodb+srv://...
SECRET_KEY=your-secret-key
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Optional
OPENAI_API_KEY=sk-...
GEMINI_API_KEY=...
QDRANT_URL=http://localhost:6333
REDIS_URL=redis://localhost:6379
```

### Production Checklist
- [ ] Set secure environment variables
- [ ] Configure MongoDB Atlas connection
- [ ] Set up SSL certificates
- [ ] Configure rate limiting
- [ ] Set up monitoring (Prometheus/Grafana)
- [ ] Configure backup strategies
- [ ] Set up CI/CD pipeline

### Monitoring & Health
```bash
# Health check
curl http://localhost:8000/health

# Metrics (if enabled)
curl http://localhost:8000/metrics
```

## 🤝 Contributing

<!-- NOT AVAILABLE -->

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Issues**: [GitHub Issues](https://github.com/LexinTosh-MeroAdda/legal_backend/issues)
- **Discussions**: [GitHub Discussions](https://github.com/LexinTosh-MeroAdda/legal_backend/discussions)
- **Email**: <EMAIL>
